@extends('layouts.app')

@section('title', 'Sidebar Toggle Test - Debug')

@push('styles')
<link rel="stylesheet" href="{{ asset('css/course-viewer.css') }}">
@endpush

@push('scripts')
<script src="{{ asset('js/course-viewer.js') }}"></script>
@endpush

@section('content')
<!-- Meta tags for JavaScript -->
<meta name="course-id" content="test-course">
<meta name="current-lecture-id" content="1">
<meta name="current-lecture-slug" content="test-lecture">
<meta name="current-chapter-slug" content="test-chapter">

<div class="min-h-screen bg-black text-white p-4">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-2xl font-bold mb-4 text-red-500">Sidebar Toggle Test Page</h1>
        
        <div class="bg-gray-800 p-4 rounded-lg mb-6">
            <h2 class="text-lg font-semibold mb-2">Debug Information</h2>
            <p class="text-sm text-gray-300 mb-2">Add <code>?debug=1</code> to the URL to enable debug logging in the browser console.</p>
            <p class="text-sm text-gray-300 mb-2">Current URL: <span id="current-url" class="text-yellow-400"></span></p>
            <p class="text-sm text-gray-300">Debug Mode: <span id="debug-status" class="text-yellow-400"></span></p>
        </div>

        <div class="bg-gray-800 p-4 rounded-lg mb-6">
            <h2 class="text-lg font-semibold mb-2">Test Instructions</h2>
            <ol class="list-decimal list-inside text-sm text-gray-300 space-y-1">
                <li>Click the "Toggle Sidebar" button multiple times</li>
                <li>Verify the sidebar opens and closes consistently</li>
                <li>Test on both desktop and mobile screen sizes</li>
                <li>Check browser console for any errors or debug messages</li>
                <li>Verify accessibility: sidebar should have proper aria-hidden states</li>
            </ol>
        </div>
    </div>
</div>

<!-- Course Viewer Container -->
<div class="course-viewer">
    <!-- Sidebar Overlay for Mobile -->
    <div class="sidebar-overlay"></div>

    <!-- Course Viewer Layout -->
    <div class="course-viewer-container">
        <!-- Course Sidebar -->
        <aside class="course-sidebar" role="complementary" aria-label="Course Navigation">
            <!-- Sidebar Header -->
            <div class="sidebar-header">
                <button class="sidebar-close-btn" aria-label="Close sidebar">
                    <i class="fas fa-times"></i>
                </button>

                <h1 class="course-title">Test Course - Sidebar Toggle</h1>
                
                <!-- Course Progress -->
                <div class="course-progress">
                    <div class="progress-bar-container">
                        <div class="progress-bar" style="width: 30%"></div>
                    </div>
                    <div class="progress-text">
                        <span class="lecture-count">3/10 lessons</span>
                        <span class="progress-percentage">30%</span>
                    </div>
                </div>
            </div>

            <!-- Curriculum Section -->
            <div class="curriculum-section">
                <h2 class="curriculum-title">
                    <button class="sidebar-hamburger-toggle" aria-label="Toggle sidebar">
                        <i class="fas fa-bars curriculum-icon"></i>
                    </button>
                    Course Content
                </h2>

                <!-- Test Chapter -->
                <div class="chapter expanded">
                    <div class="chapter-header">
                        <h3 class="chapter-title">
                            <span>1. Test Chapter</span>
                            <i class="fas fa-chevron-down chapter-toggle"></i>
                        </h3>
                    </div>

                    <div class="lectures-list">
                        <a href="#" class="lecture-item active" data-lecture-id="1">
                            <div class="lecture-status current">
                                <i class="fas fa-play"></i>
                            </div>
                            <div class="lecture-content">
                                <h4 class="lecture-title">Test Lecture 1 (Current)</h4>
                            </div>
                        </a>
                        
                        <a href="#" class="lecture-item completed" data-lecture-id="2">
                            <div class="lecture-status completed">
                                <i class="fas fa-check"></i>
                            </div>
                            <div class="lecture-content">
                                <h4 class="lecture-title">Test Lecture 2 (Completed)</h4>
                            </div>
                        </a>
                        
                        <a href="#" class="lecture-item" data-lecture-id="3">
                            <div class="lecture-status pending">3</div>
                            <div class="lecture-content">
                                <h4 class="lecture-title">Test Lecture 3 (Pending)</h4>
                            </div>
                        </a>
                    </div>
                </div>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="course-content">
            <div class="content-header">
                <div class="header-controls">
                    <button class="sidebar-toggle" aria-label="Toggle sidebar">
                        <i class="fas fa-bars"></i>
                    </button>
                    <h1 class="text-lg font-semibold">Sidebar Toggle Test</h1>
                </div>
            </div>
            
            <div class="p-6">
                <div class="bg-gray-900 p-6 rounded-lg">
                    <h2 class="text-xl font-bold mb-4">Test Results</h2>
                    <div id="test-results" class="space-y-2">
                        <p class="text-gray-300">Click the sidebar toggle buttons to test functionality...</p>
                    </div>
                    
                    <div class="mt-6">
                        <button id="run-tests" class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded">
                            Run Automated Tests
                        </button>
                        <button id="clear-results" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded ml-2">
                            Clear Results
                        </button>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Update debug information
    document.getElementById('current-url').textContent = window.location.href;
    document.getElementById('debug-status').textContent = window.location.search.includes('debug=1') ? 'Enabled' : 'Disabled';
    
    let testCount = 0;
    const resultsContainer = document.getElementById('test-results');
    
    function addTestResult(message, success = true) {
        testCount++;
        const result = document.createElement('div');
        result.className = `text-sm ${success ? 'text-green-400' : 'text-red-400'}`;
        result.innerHTML = `${testCount}. ${message}`;
        resultsContainer.appendChild(result);
    }
    
    // Automated tests
    document.getElementById('run-tests').addEventListener('click', function() {
        addTestResult('Starting automated sidebar toggle tests...');
        
        // Test 1: Check if CourseViewer is initialized
        if (window.CourseViewer) {
            addTestResult('✓ CourseViewer class is available');
        } else {
            addTestResult('✗ CourseViewer class not found', false);
        }
        
        // Test 2: Check if sidebar elements exist
        const sidebar = document.querySelector('.course-sidebar');
        const toggleBtn = document.querySelector('.sidebar-toggle');
        const hamburgerBtn = document.querySelector('.sidebar-hamburger-toggle');
        
        if (sidebar) addTestResult('✓ Sidebar element found');
        else addTestResult('✗ Sidebar element not found', false);
        
        if (toggleBtn) addTestResult('✓ Main toggle button found');
        else addTestResult('✗ Main toggle button not found', false);
        
        if (hamburgerBtn) addTestResult('✓ Hamburger toggle button found');
        else addTestResult('✗ Hamburger toggle button not found', false);
        
        // Test 3: Check event binding attributes
        if (toggleBtn && toggleBtn.hasAttribute('data-event-bound')) {
            addTestResult('✓ Main toggle button has event binding marker');
        } else {
            addTestResult('✗ Main toggle button missing event binding marker', false);
        }
        
        if (hamburgerBtn && hamburgerBtn.hasAttribute('data-event-bound')) {
            addTestResult('✓ Hamburger toggle button has event binding marker');
        } else {
            addTestResult('✗ Hamburger toggle button missing event binding marker', false);
        }
        
        addTestResult('Automated tests completed. Now test manual clicking...');
    });
    
    document.getElementById('clear-results').addEventListener('click', function() {
        resultsContainer.innerHTML = '<p class="text-gray-300">Results cleared. Click "Run Automated Tests" or test manually...</p>';
        testCount = 0;
    });
});
</script>
@endsection
